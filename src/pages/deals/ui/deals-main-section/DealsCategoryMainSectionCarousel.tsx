import { Typography } from '@components/typography';
import { Button } from '@components/ui/button';
import { LOCIZE_DEALS_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { useInfiniteDeals } from '@entities/deals/hooks/useInfiniteDeals';
import { useIsMobileView } from '@hooks/system';
import { getDealCategoryIcon } from '@pages/deals/config';
import type { DealCategoryName } from '@pages/deals/types';
import { DealCard } from '@pages/deals/ui/DealCard';
import { scrollToCategory } from '@pages/deals/utils';
import { useTranslation } from 'react-i18next';

import { getDealsRouteApi } from '@/shared/utils/dealsRouteApi';

import { CardsCarousel } from '../../../../shared/components/ui/CardsCarousel';

const routeApi = getDealsRouteApi();

type DealsCategoryCarouselProps = {
  category: DealCategoryName;
  title: string;
};

export const DealsCategoryMainSectionCarousel = ({
  category,
  title,
}: DealsCategoryCarouselProps) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.deals);
  const {
    data: deals,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfiniteDeals({
    variables: {
      categories: [category],
    },
    limit: 5,
  });
  const navigate = routeApi.useNavigate();
  const isMobileView = useIsMobileView();

  if (!deals || deals.length === 0) {
    return null;
  }

  const Icon = getDealCategoryIcon(category);

  const onFeatureSectionRedirect = () => {
    navigate({
      search: {
        category: category,
      },
      resetScroll: false,
    });

    scrollToCategory(category, { isMobileView });
  };

  return (
    <div className={'mb-20 grid gap-1 px-0 md:px-12'}>
      <CardsCarousel
        cards={deals}
        title={
          <button
            type="button"
            className="flex items-center gap-3 hover:text-neutral-700"
            onClick={onFeatureSectionRedirect}
          >
            <Icon />
            <Typography
              tag="h2"
              variant={isMobileView ? 'xxs' : 'xs'}
              className={'flex items-center gap-3 text-left'}
            >
              {title}
            </Typography>
          </button>
        }
        actionBtn={
          <Button
            onClick={onFeatureSectionRedirect}
            size="small"
            variant="grey"
            className={'text-center bg-neutral-50'}
          >
            {t(LOCIZE_DEALS_KEYS.dealsViewAll)}
          </Button>
        }
        renderCard={(card) => {
          return <DealCard deal={card} />;
        }}
        hasNextPage={hasNextPage}
        fetchNextPage={fetchNextPage}
        isFetchingNextPage={isFetchingNextPage}
      />
    </div>
  );
};
