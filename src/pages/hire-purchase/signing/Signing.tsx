import { SigningPageViewTypes } from 'app-constants';
import { AppContactUs, AppLoader, AppLoaderContentWrapper } from 'components';
import { useSigningPageContext } from 'context/hire-purchase';
import { SigningPageLogicProvider } from 'context/hire-purchase/SigningPageLogicProvider';

import { PendingView } from './PendingView';
import { PinConfirmationView } from './PinConfirmationView';
import { SigningView } from './SigningView';

const Page = () => {
  const { processingSigningPage, signingPageLoaded, signingPageViewType } =
    useSigningPageContext();

  if (!signingPageLoaded) {
    return <AppLoader isRelative />;
  }

  const renderSigningPageContent = () => {
    switch (signingPageViewType) {
      case SigningPageViewTypes.pinConfirmation:
        return <PinConfirmationView />;
      case SigningPageViewTypes.signing:
        return <SigningView />;
      case SigningPageViewTypes.pending:
        return <PendingView />;
      default:
        return <SigningView />;
    }
  };

  return (
    <AppLoaderContentWrapper shouldShowLoader={processingSigningPage}>
      {renderSigningPageContent()}
      <AppContactUs />
    </AppLoaderContentWrapper>
  );
};

const SigningPage = () => (
  <SigningPageLogicProvider>
    <Page />
  </SigningPageLogicProvider>
);

export default SigningPage;
