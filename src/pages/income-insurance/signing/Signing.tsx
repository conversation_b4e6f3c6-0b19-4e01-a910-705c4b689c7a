import { SigningPageViewTypes } from 'app-constants';
import { useIncomeSigningPageContext } from 'context/income-insurance';
import { SigningPageLogicProvider } from 'context/income-insurance/SigningPageLogicProvider';
import { lazy } from 'react';

const PendingView = lazy(() =>
  import('../../../components/default-loader/DefaultLoader').then((module) => ({
    default: module.DefaultLoader,
  })),
);

const SigningView = lazy(() =>
  import('./SigningView').then((module) => ({
    default: module.SigningView,
  })),
);

const PinConfirmationView = lazy(() =>
  import('./PinConfirmationView').then((module) => ({
    default: module.PinConfirmationView,
  })),
);

const Page = () => {
  const { signingPageViewType } = useIncomeSigningPageContext();

  const renderSigningPageContent = () => {
    switch (signingPageViewType) {
      case SigningPageViewTypes.pinConfirmation:
        return <PinConfirmationView />;
      case SigningPageViewTypes.signing:
        return <SigningView />;
      case SigningPageViewTypes.pending:
        return <PendingView />;
      default:
        return <SigningView />;
    }
  };

  return renderSigningPageContent();
};

const SigningPage = () => (
  <SigningPageLogicProvider>
    <Page />
  </SigningPageLogicProvider>
);

export default SigningPage;
