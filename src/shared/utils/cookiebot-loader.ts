const COOKIEBOT_IDS = {
  et: '9a2e740d-9929-4e9e-83da-13420793ea4c',
  lv: '72fc07fe-4606-43e2-b090-638c0c480f51',
  lt: '0bb1d40b-c95b-4ca7-b0ec-658d7324ba6d',
} as const;

type Region = keyof typeof COOKIEBOT_IDS;

export const loadCookiebot = (region: string): void => {
  const validRegion = (
    COOKIEBOT_IDS[region as Region] ? region : 'et'
  ) as Region;
  const cookiebotId = COOKIEBOT_IDS[validRegion];

  if (document.getElementById('Cookiebot')) {
    return;
  }

  const script = document.createElement('script');
  script.id = 'Cookiebot';
  script.src = 'https://consent.cookiebot.com/uc.js';
  script.setAttribute('data-cbid', cookiebotId);
  script.setAttribute('data-blockingmode', 'auto');
  script.type = 'text/javascript';

  document.head.appendChild(script);
};
