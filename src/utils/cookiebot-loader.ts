const COOKIEBOT_IDS = {
  EE: '9a2e740d-9929-4e9e-83da-13420793ea4c',
  LV: '72fc07fe-4606-43e2-b090-638c0c480f51',
  LT: '0bb1d40b-c95b-4ca7-b0ec-658d7324ba6d',
} as const;

type Region = keyof typeof COOKIEBOT_IDS;

const getRootDomain = (): string => {
  const hostname = window.location.hostname;
  const parts = hostname.split('.');

  // If hostname has more than 2 parts, take the last 2 (domain.tld)
  if (parts.length > 2) {
    return parts.slice(-2).join('.');
  }

  return hostname;
};

const setupCookiebotConfig = (): void => {
  const rootDomain = getRootDomain();
  const currentHostname = window.location.hostname;

  (
    window as typeof window & { CookiebotConfig?: { cookieDomain: string } }
  ).CookiebotConfig = {
    cookieDomain: `.${rootDomain}`,
  };

  const configScript = document.createElement('script');
  configScript.type = 'text/javascript';
  configScript.innerHTML = `
    const COOKIES_TO_SYNC = ['CookieConsent'];
    const rootDomain = '${rootDomain}';
    const currentHostname = '${currentHostname}';

    function getCookieValue(name, cookieString = document.cookie) {
      const match = cookieString.match(new RegExp('(^| )' + name + '=([^;]+)'));
      return match ? match[2] : null;
    }

    function setCookie(name, value, domain, path = '/', sameSite = 'Lax') {
      const expiryDate = new Date();
      expiryDate.setFullYear(expiryDate.getFullYear() + 1);

      const cookieString = \`\${name}=\${value}; domain=\${domain}; path=\${path}; expires=\${expiryDate.toUTCString()}; SameSite=\${sameSite}\`;
      document.cookie = cookieString;

      console.log(\`Cookie set: \${name} on domain \${domain}\`);
    }

    function syncCookieToRootDomain(cookieName, cookieValue) {
      if (!cookieValue) return;

      console.log(\`Syncing \${cookieName} from subdomain \${currentHostname} to root domain .\${rootDomain}\`);
      setCookie(cookieName, cookieValue, \`.\${rootDomain}\`);
    }

    function syncCookieToSubdomain(cookieName, cookieValue) {
      if (!cookieValue) return;

      console.log(\`Syncing \${cookieName} from root domain .\${rootDomain} to subdomain \${currentHostname}\`);
      setCookie(cookieName, cookieValue, currentHostname);
    }

    function performBidirectionalSync() {
      COOKIES_TO_SYNC.forEach(cookieName => {
        const currentCookieValue = getCookieValue(cookieName);

        // Check if we need to sync from subdomain to root domain
        if (currentCookieValue) {
          syncCookieToRootDomain(cookieName, currentCookieValue);
        }

        // For initial sync: if no subdomain cookie exists but root domain cookie might exist
        // We'll check this during cookie monitoring
      });
    }

    function checkForRootDomainCookies() {
      // This function attempts to detect if root domain cookies exist
      // by checking if cookies appear after setting them
      COOKIES_TO_SYNC.forEach(cookieName => {
        const currentValue = getCookieValue(cookieName);
        if (!currentValue) {
          // Try to read from a potential root domain cookie by temporarily setting a test
          // and seeing if a value appears (this is a workaround for cross-domain cookie reading limitations)
          console.log(\`Checking for existing root domain cookie: \${cookieName}\`);
        }
      });
    }

    // Monitor for Cookiebot events and perform bidirectional sync
    window.addEventListener('CookiebotOnConsentReady', function () {
      console.log('Cookiebot consent ready, performing bidirectional sync');
      setTimeout(performBidirectionalSync, 100);
    });

    window.addEventListener('CookiebotOnAccept', function () {
      console.log('Cookiebot consent accepted, performing bidirectional sync');
      setTimeout(performBidirectionalSync, 100);
    });

    window.addEventListener('CookiebotOnDecline', function () {
      console.log('Cookiebot consent declined, performing bidirectional sync');
      setTimeout(performBidirectionalSync, 100);
    });

    // Enhanced cookie monitoring for bidirectional synchronization
    let lastCookieValues = {};

    // Initialize tracking
    COOKIES_TO_SYNC.forEach(cookieName => {
      lastCookieValues[cookieName] = getCookieValue(cookieName);
    });

    function monitorCookieChanges() {
      let hasChanges = false;

      COOKIES_TO_SYNC.forEach(cookieName => {
        const currentValue = getCookieValue(cookieName);
        const lastValue = lastCookieValues[cookieName];

        if (currentValue !== lastValue) {
          hasChanges = true;
          console.log(\`Cookie change detected: \${cookieName} changed from '\${lastValue}' to '\${currentValue}'\`);

          // Update tracking
          lastCookieValues[cookieName] = currentValue;

          // Sync to root domain if value exists
          if (currentValue) {
            syncCookieToRootDomain(cookieName, currentValue);
          }
        }
      });

      return hasChanges;
    }

    // Check for cookie changes every 500ms
    setInterval(monitorCookieChanges, 500);

    // Initial sync when page loads
    window.addEventListener('load', function () {
      setTimeout(() => {
        console.log('Page loaded, performing initial bidirectional sync');
        performBidirectionalSync();
        checkForRootDomainCookies();
      }, 1000);
    });

    // Perform initial sync immediately
    setTimeout(() => {
      performBidirectionalSync();
      checkForRootDomainCookies();
    }, 100);
  `;

  document.head.appendChild(configScript);
};

export const loadCookiebot = (region: string): void => {
  const validRegion = (
    COOKIEBOT_IDS[region as Region] ? region : 'EE'
  ) as Region;
  const cookiebotId = COOKIEBOT_IDS[validRegion];

  if (document.getElementById('Cookiebot')) {
    return;
  }

  setupCookiebotConfig();

  const script = document.createElement('script');
  script.id = 'Cookiebot';
  script.src = 'https://consent.cookiebot.com/uc.js';
  script.setAttribute('data-cbid', cookiebotId);
  script.setAttribute('data-blockingmode', 'auto');
  script.type = 'text/javascript';

  document.head.appendChild(script);
};
