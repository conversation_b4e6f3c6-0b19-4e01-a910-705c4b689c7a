const COOKIEBOT_IDS = {
  EE: '9a2e740d-9929-4e9e-83da-13420793ea4c',
  LV: '72fc07fe-4606-43e2-b090-638c0c480f51',
  LT: '0bb1d40b-c95b-4ca7-b0ec-658d7324ba6d',
} as const;

type Region = keyof typeof COOKIEBOT_IDS;

const getRootDomain = (): string => {
  const hostname = window.location.hostname;
  const parts = hostname.split('.');

  // If hostname has more than 2 parts, take the last 2 (domain.tld)
  if (parts.length > 2) {
    return parts.slice(-2).join('.');
  }

  return hostname;
};

const setupCookiebotConfig = (): void => {
  const rootDomain = getRootDomain();

  (
    window as typeof window & { CookiebotConfig?: { cookieDomain: string } }
  ).CookiebotConfig = {
    cookieDomain: `.${rootDomain}`,
  };

  const configScript = document.createElement('script');
  configScript.type = 'text/javascript';
  configScript.innerHTML = `
    function rewriteCookieConsentDomain() {
      const cookieMatch = document.cookie.match(/CookieConsent=([^;]+)/);

      if (cookieMatch) {
        const cookieValue = cookieMatch[1];
        console.log(
          'Found existing CookieConsent, rewriting with domain .${rootDomain}',
        );

        const expiryDate = new Date();
        expiryDate.setFullYear(expiryDate.getFullYear() + 1);

        document.cookie = \`CookieConsent=\${cookieValue}; domain=.${rootDomain}; path=/; expires=\${expiryDate.toUTCString()}; SameSite=Lax\`;

        console.log('CookieConsent rewritten with domain .${rootDomain}');
        return true;
      }

      console.log('No CookieConsent found to rewrite');
      return false;
    }

    // Monitor for Cookiebot events and rewrite cookie domain
    window.addEventListener('CookiebotOnConsentReady', function () {
      console.log('Cookiebot consent ready, rewriting cookie domain');
      rewriteCookieConsentDomain();
    });

    window.addEventListener('CookiebotOnAccept', function () {
      console.log('Cookiebot consent accepted, rewriting cookie domain');
      setTimeout(rewriteCookieConsentDomain, 100);
    });

    window.addEventListener('CookiebotOnDecline', function () {
      console.log('Cookiebot consent declined, rewriting cookie domain');
      setTimeout(rewriteCookieConsentDomain, 100);
    });

    // Monitor for cookie changes and rewrite domain when CookieConsent appears
    let lastCookieString = document.cookie;

    function checkForCookieConsentChanges() {
      if (document.cookie !== lastCookieString) {
        lastCookieString = document.cookie;

        // If CookieConsent cookie exists, rewrite it with correct domain
        if (document.cookie.includes('CookieConsent=')) {
          console.log('CookieConsent detected, rewriting domain');
          setTimeout(rewriteCookieConsentDomain, 50);
        }
      }
    }

    // Check for cookie changes every 500ms
    setInterval(checkForCookieConsentChanges, 500);

    // Also check once when page loads
    window.addEventListener('load', function () {
      setTimeout(rewriteCookieConsentDomain, 1000);
    });
  `;

  document.head.appendChild(configScript);
};

export const loadCookiebot = (region: string): void => {
  const validRegion = (
    COOKIEBOT_IDS[region as Region] ? region : 'EE'
  ) as Region;
  const cookiebotId = COOKIEBOT_IDS[validRegion];

  if (document.getElementById('Cookiebot')) {
    return;
  }

  setupCookiebotConfig();

  const script = document.createElement('script');
  script.id = 'Cookiebot';
  script.src = 'https://consent.cookiebot.com/uc.js';
  script.setAttribute('data-cbid', cookiebotId);
  script.setAttribute('data-blockingmode', 'auto');
  script.type = 'text/javascript';

  document.head.appendChild(script);
};
