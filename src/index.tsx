import 'styles/global.scss';
import 'styles/index.css';

import { App } from 'App';
import { AppProviders } from 'components';
import { isDevelopment, region } from 'environment';
import { createRoot } from 'react-dom/client';
import { initializeSentry } from 'services';
import { loadCookiebot } from 'utils/cookiebot-loader';

loadCookiebot(region);

if (!isDevelopment) initializeSentry();

const rootElement = document.getElementById('root');
if (!rootElement) throw new Error('Failed to find the root element');

createRoot(rootElement).render(
  <AppProviders>
    <App />
  </AppProviders>,
);
