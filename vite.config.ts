import * as path from 'node:path';

import { sentryVitePlugin } from '@sentry/vite-plugin';
import react from '@vitejs/plugin-react-swc';
import { defineConfig, loadEnv } from 'vite';
import svgr from 'vite-plugin-svgr';
import webfontDownload from 'vite-plugin-webfont-dl';
import viteTsconfigPaths from 'vite-tsconfig-paths';

const port = 3010;

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');

  const plugins = [
    react(),
    viteTsconfigPaths(),
    svgr(),
    webfontDownload([
      'https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap',
    ]),
  ];

  if (process.env.NODE_ENV === 'production') {
    plugins.push(
      sentryVitePlugin({
        authToken: process.env.SENTRY_AUTH_TOKEN,
        org: 'esto-as',
        project: 'purchase-flow-front',
      }),
    );
  }

  return {
    build: {
      sourcemap: true,
      outDir: 'build',
    },
    plugins,
    css: {
      modules: {
        localsConvention: 'camelCase',
      },
    },
    resolve: {
      alias: {
        styles: path.resolve(__dirname, './src/styles'),
        icons: path.resolve(__dirname, './src/icons'),
        utils: path.resolve(__dirname, './src/utils'),
      },
    },
    define: {
      'process.env': env,
    },
    server: {
      host: true, // This allows connections from Docker
      port,
    },
  };
});
